import {
  openConfirmD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  openUpdate<PERSON><PERSON><PERSON>tom,
} from "@/atoms/users/open-users-atoms";
import { selectedUser<PERSON><PERSON> } from "@/atoms/users/selected-users-atom";
import { usersAtom } from "@/atoms/users/users-atom";
import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ConfirmDeleteUserDialog from "@/components/users/confirm-delete-user-dialog";
import CreateUserDialog from "@/components/users/create-user-dialog";
import EditUserDialog from "@/components/users/edit-user-dialog";
import UsersPageHeader from "@/components/users/users-page-header";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import { useIsMobile } from "@/hooks/use-mobile";
import useTableResize from "@/hooks/use-table-resize";
import type { User } from "@/types/users";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

const columns: ColumnDef<User>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Name" />;
    },
    cell: ({ row }) => {
      const name = row.original.name;
      return <div>{name}</div>;
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Email" />;
    },
    cell: ({ row }) => {
      const email = row.original.email;
      return <div>{email}</div>;
    },
  },
  {
    accessorKey: "phone",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Phone" />;
    },
    cell: ({ row }) => {
      const phone = row.original.phone;
      return <div>{phone}</div>;
    },
  },
  {
    accessorKey: "is_active",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Is Active" />;
    },
    cell: ({ row }) => {
      return <div>{row.original.is_active ? "True" : "False"}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-start">Actions</div>,
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="size-7"
                onClick={() => {
                  selectedUserAtom.change("slug", row.original.slug);
                  openUpdateUserAtom.open();
                }}
              >
                <Edit />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="size-7"
                onClick={() => {
                  selectedUserAtom.change("slug", row.original.slug);
                  openConfirmDeleteUserAtom.open();
                }}
              >
                <Trash2 color="red" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete</p>
            </TooltipContent>
          </Tooltip>
        </div>
      );
    },
  },
];

export default function UsersPage() {
  const isMobile = useIsMobile();
  const [isLoading, setIsLoading] = useState(false);

  const { users } = usersAtom.useValue();
  const { handleResize, tableSize, minSize } = useTableResize();

  useEffect(() => {
    async function fetchUsers() {
      setIsLoading(true);
      await usersAtom.getUsers();
      setIsLoading(false);
    }

    fetchUsers();
  }, []);

  if (isLoading) {
    return <OverlayLoader />;
  }

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3 p-2">
          <UsersPageHeader />
          <DataTable columns={columns} data={users} />
        </div>
        <div className="h-[500px] w-full p-2">{/* <AreaMap /> */}</div>

        {/* modals */}

        <ConfirmDeleteUserDialog />
        <CreateUserDialog />
        <EditUserDialog />
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-3 p-4"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={minSize}
      >
        <UsersPageHeader />
        <DataTable columns={columns} data={users} />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">{/* <AreaMap /> */}</ResizablePanel>

      {/* modals */}
      <ConfirmDeleteUserDialog />
      <CreateUserDialog />
      <EditUserDialog />
    </ResizablePanelGroup>
  );
}
