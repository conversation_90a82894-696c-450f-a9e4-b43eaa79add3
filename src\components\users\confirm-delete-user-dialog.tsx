import useConfirmDeleteUserDialog from "@/hooks/users/use-confirm-delete-user-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteUserDialog() {
  const {
    closeDialog,
    deleteUser,
    isLoading,
    isLoadingFetchUser,
    isOpened,
    oneUser,
    t,
  } = useConfirmDeleteUserDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        {isLoadingFetchUser ? (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("users.confirm_delete.deleteUserTitle")}
              </DialogTitle>
              <DialogDescription>
                {t("users.confirm_delete.deleteUserDescription")}
              </DialogDescription>
            </DialogHeader>

            <OverlayLoader inCenter={false} />
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("users.confirm_delete.deleteUserTitle")} - ( {oneUser?.name}{" "}
                )
              </DialogTitle>
              <DialogDescription>
                {t("users.confirm_delete.deleteUserDescription")}
              </DialogDescription>
            </DialogHeader>

            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">
                  {t("users.confirm_delete.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                onClick={deleteUser}
                disabled={isLoading}
              >
                {isLoading
                  ? t("users.confirm_delete.deleting")
                  : t("users.confirm_delete.delete")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
