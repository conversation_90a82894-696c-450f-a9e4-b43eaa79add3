import { openCreateTrailerAtom } from "@/atoms/trailers/open-trailers-atoms";
import { Plus } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

export default function TrailersPageHeader() {
  return (
    <div className="flex items-center justify-between">
      <h1 className="text-primary text-2xl font-bold">Trailers</h1>
      <div className="flex gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="secondary"
              size="icon"
              className="size-7"
              onClick={openCreateTrailerAtom.open}
            >
              <Plus />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Add Trailer</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}
