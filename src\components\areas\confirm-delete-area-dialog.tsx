import useConfirmDeleteAreaDialog from "@/hooks/areas/use-confirm-delete-area-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteAreaDialog() {
  const {
    area,
    closeDialog,
    deleteArea,
    isLoading,
    isLoadingFetchArea,
    isOpened,
    t,
  } = useConfirmDeleteAreaDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        {isLoadingFetchArea ? (
          <>
            <DialogHeader>
              <DialogTitle>{t("areas.confirm_delete.title")}</DialogTitle>
              <DialogDescription>
                {t("areas.confirm_delete.description")}
              </DialogDescription>
            </DialogHeader>

            <OverlayLoader inCenter={false} />
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("areas.confirm_delete.title")} ( {area?.name} )
              </DialogTitle>
              <DialogDescription>
                {t("areas.confirm_delete.description")}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline" onClick={closeDialog}>
                  {t("areas.confirm_delete.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                onClick={deleteArea}
                disabled={isLoading}
              >
                {isLoading
                  ? t("areas.confirm_delete.loading")
                  : t("areas.confirm_delete.delete")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
