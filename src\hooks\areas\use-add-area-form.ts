import { areaGroupsAtom } from "@/atoms/area-groups/area-groups-atom";
import { areaShapePointsAtom } from "@/atoms/areas/area-shape-points-atom";
import { areasAtom } from "@/atoms/areas/areas-atom";
import { selectedDrawerType<PERSON>tom } from "@/atoms/areas/selected-drawer-type-atom";
import { URLS } from "@/utils/urls";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useNavigate } from "react-router";
import { z } from "zod";

const FormSchema = z.object({
  name: z.string().nonempty("Name is required"),
  code: z.string().nonempty("Code is required"),
  type: z.number().min(1, "Type is required"),
  color: z.string().nonempty("Color is required"),
  is_personal: z.boolean(),
  area_group_id: z.number().min(1, "Area Group is required"),
});

export default function useAddAreaForm() {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      code: "",
      type: 0,
      color: "#000000",
      is_personal: false,
      area_group_id: 0,
    },
  });

  const { areaGroups } = areaGroupsAtom.useValue();

  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    areaGroupsAtom.getAreaGroups();
  }, []);

  const { value: points } = areaShapePointsAtom.useValue();

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    if (!points) {
      toast.error("Please draw an area on the map");
      return;
    }

    setIsLoading(true);

    await areasAtom.createArea(
      {
        ...data,
        points,
      },
      () => {
        navigate(URLS.areas);
        selectedDrawerTypeAtom.change("selectedType", 0);
        areaShapePointsAtom.change("value", null);
      },
    );

    setIsLoading(false);
  }

  useEffect(() => {
    return () => {
      selectedDrawerTypeAtom.reset();
      areaShapePointsAtom.reset();
    };
  }, []);

  return {
    form,
    isLoading,
    onSubmit,
    areaGroups,
  };
}
