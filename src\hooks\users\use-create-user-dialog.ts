import { openCreate<PERSON>ser<PERSON>tom } from "@/atoms/users/open-users-atoms";
import { users<PERSON>tom } from "@/atoms/users/users-atom";
import type { CreateUserFormData } from "@/types/users";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const FormSchema = z
  .object({
    name: z.string().nonempty("Name is required"),
    email: z.string().email("Invalid email"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    password_confirmation: z
      .string()
      .min(8, "Password confirmation must be at least 8 characters"),
    phone_code: z.string().nonempty("Phone code is required"),
    phone: z.string().nonempty("Phone number is required"),
    lang: z.string().nonempty("Language is required"),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords do not match",
    path: ["password_confirmation"],
  });

export default function useCreateUserDialog() {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      password_confirmation: "",
      phone_code: "966",
      phone: "",
      lang: "en",
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const isOpened = openCreateUserAtom.useOpened();

  const closeDialog = () => {
    openCreateUserAtom.close();
    form.reset();
  };

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: CreateUserFormData = {
      name: data.name,
      email: data.email,
      password: data.password,
      password_confirmation: data.password_confirmation,
      phone_code: data.phone_code,
      phone: data.phone,
      lang: data.lang as "en" | "ar",
    };

    setIsLoading(true);

    await usersAtom.createUser(formData, () => {
      closeDialog();
    });

    setIsLoading(false);
  }

  return {
    form,
    isLoading,
    isOpened,
    closeDialog,
    onSubmit,
  };
}
