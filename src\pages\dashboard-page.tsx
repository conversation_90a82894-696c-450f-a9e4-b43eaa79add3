import { authAtom } from "@/atoms/auth/auth-atom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { URLS } from "@/utils/urls";
import {
  Activity,
  AreaChart,
  GalleryVerticalEnd,
  MapPin,
  Train,
  TrendingUp,
  Users,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";

export default function DashboardPage() {
  const { user } = authAtom.useValue();
  const { t } = useTranslation();

  // Mock data - in a real app, this would come from API calls
  const stats = {
    areas: 12,
    trailers: 45,
    drivers: 28,
    users: 8,
  };

  const quickActions = [
    {
      title: "Areas",
      description: "Manage geofenced areas",
      icon: AreaChart,
      url: URLS.areas,
      color: "bg-blue-500",
    },
    {
      title: "Trailers",
      description: "Track and manage trailers",
      icon: Train,
      url: URLS.trailers,
      color: "bg-green-500",
    },
    {
      title: "Drivers",
      description: "Manage driver profiles",
      icon: GalleryVerticalEnd,
      url: URLS.drivers,
      color: "bg-orange-500",
    },
    {
      title: "Users",
      description: "System user management",
      icon: Users,
      url: URLS.users,
      color: "bg-purple-500",
    },
  ];

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Welcome Header */}
      <div className="flex flex-col gap-2">
        <h1 className="text-primary text-3xl font-bold">
          Welcome back, {user?.name || "User"}!
        </h1>
        <p className="text-muted-foreground">
          Here's an overview of your GPS tracking system
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Areas</CardTitle>
            <AreaChart className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.areas}</div>
            <p className="text-muted-foreground text-xs">
              <TrendingUp className="mr-1 inline h-3 w-3" />
              +2 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Trailers
            </CardTitle>
            <Train className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.trailers}</div>
            <p className="text-muted-foreground text-xs">
              <Activity className="mr-1 inline h-3 w-3" />
              42 active now
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Drivers</CardTitle>
            <GalleryVerticalEnd className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.drivers}</div>
            <p className="text-muted-foreground text-xs">
              <Activity className="mr-1 inline h-3 w-3" />
              24 on duty
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Users</CardTitle>
            <Users className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.users}</div>
            <p className="text-muted-foreground text-xs">
              <Activity className="mr-1 inline h-3 w-3" />5 online now
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {quickActions.map((action) => (
          <Card
            key={action.title}
            className="cursor-pointer transition-shadow hover:shadow-md"
          >
            <Link to={action.url} className="block">
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <div
                  className={`rounded-lg p-2 ${action.color} mr-3 text-white`}
                >
                  <action.icon className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-base">{action.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-sm">
                  {action.description}
                </p>
              </CardContent>
            </Link>
          </Card>
        ))}
      </div>

      {/* Map Overview Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Live Tracking Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted flex h-64 items-center justify-center rounded-lg">
              <div className="text-center">
                <MapPin className="text-muted-foreground mx-auto mb-2 h-12 w-12" />
                <p className="text-muted-foreground">
                  Interactive map will be displayed here
                </p>
                <p className="text-muted-foreground mt-1 text-sm">
                  Showing real-time locations of trailers and drivers
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">
                    Trailer T-001 entered Area A-5
                  </p>
                  <p className="text-muted-foreground text-xs">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">
                    Driver John Doe started shift
                  </p>
                  <p className="text-muted-foreground text-xs">
                    15 minutes ago
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">
                    New area "Warehouse B" created
                  </p>
                  <p className="text-muted-foreground text-xs">1 hour ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-2 w-2 rounded-full bg-red-500"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">
                    Trailer T-012 left designated area
                  </p>
                  <p className="text-muted-foreground text-xs">2 hours ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
