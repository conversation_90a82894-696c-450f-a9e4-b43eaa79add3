import { areas<PERSON>tom } from "@/atoms/areas/areas-atom";
import { openConfirmDeleteAreaDialogAtom } from "@/atoms/areas/open-confirm-delete-area-dialog-atom";
import { selectedAreaToDeleteAtom } from "@/atoms/areas/selected-area-to-delete-atom";
import { AreaMap } from "@/components/areas/area-map";
import AreasPageHeader from "@/components/areas/areas-page-header";
import ConfirmDeleteAreaDialog from "@/components/areas/confirm-delete-area-dialog";
import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import { useIsMobile } from "@/hooks/use-mobile";
import useTableResize from "@/hooks/use-table-resize";
import type { Area } from "@/types/areas";
import { URLS } from "@/utils/urls";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Locate, MessageCircle, Trash2, Wifi } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router";

const columns: ColumnDef<Area>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Name" />;
    },
    cell: ({ row }) => {
      const name = row.original.name;
      return <div>{name}</div>;
    },
  },
  {
    accessorKey: "type",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Type" />;
    },
    cell: ({ row }) => {
      const type = row.original.type;
      return <div>{type === 1 ? "Circle" : "Polygon"}</div>;
    },
  },
  {
    accessorKey: "area_group",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Area Group" />;
    },
    cell: ({ row }) => {
      return <div>{row.original.area_group}</div>;
    },
  },
  {
    accessorKey: "code",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Code" />;
    },
    cell: ({ row }) => {
      return <div>{row.original.code}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-start">Actions</div>,
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <MessageCircle />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Message</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Locate />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Track</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Wifi />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Data Accuracy</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Link to={`${URLS.editArea}/${row.original.slug}`}>
                <Button variant="secondary" size="icon" className="size-7">
                  <Edit />
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="size-7"
                onClick={() => {
                  openConfirmDeleteAreaDialogAtom.open();
                  selectedAreaToDeleteAtom.change("slug", row.original.slug);
                }}
              >
                <Trash2 color="red" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete</p>
            </TooltipContent>
          </Tooltip>
        </div>
      );
    },
  },
];

export default function AreasPage() {
  const { handleResize, tableSize, minSize } = useTableResize();

  const isMobile = useIsMobile();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function fetchAreas() {
      setIsLoading(true);
      await areasAtom.getAreas();
      setIsLoading(false);
    }

    fetchAreas();
  }, []);

  const { areas } = areasAtom.useValue();

  if (isLoading) {
    return <OverlayLoader />;
  }

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3 p-2">
          <AreasPageHeader />
          <DataTable columns={columns} data={areas} />
        </div>
        <div className="h-[500px] w-full p-2">
          <AreaMap />
        </div>

        <ConfirmDeleteAreaDialog />
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-3 p-4"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={minSize}
      >
        <AreasPageHeader />
        <DataTable columns={columns} data={areas} />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">
        <AreaMap />
      </ResizablePanel>

      <ConfirmDeleteAreaDialog />
    </ResizablePanelGroup>
  );
}
