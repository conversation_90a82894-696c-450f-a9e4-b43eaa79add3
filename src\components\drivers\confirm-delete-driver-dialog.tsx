import useConfirmDeleteDriverDialog from "@/hooks/drivers/use-confirm-delete-driver-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteDriverDialog() {
    const {
        closeDialog,
        deleteDriver,
        isLoading,
        isLoadingFetchDriver,
        isOpened,
        oneDriver,
        t,
    } = useConfirmDeleteDriverDialog();

    return (
        <Dialog open={isOpened} onOpenChange={closeDialog}>
            <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
                {isLoadingFetchDriver ? (
                    <>
                        <DialogHeader>
                            <DialogTitle>
                                {t("drivers.confirm_delete.deleteDriverTitle")}
                            </DialogTitle>
                            <DialogDescription>
                                {t("drivers.confirm_delete.deleteDriverDescription")}
                            </DialogDescription>
                        </DialogHeader>

                        <OverlayLoader inCenter={false} />
                    </>
                ) : (
                    <>
                        <DialogHeader>
                            <DialogTitle>
                                {t("drivers.confirm_delete.deleteDriverTitle")} - ({" "}
                                {oneDriver?.name} )
                            </DialogTitle>
                            <DialogDescription>
                                {t("drivers.confirm_delete.deleteDriverDescription")}
                            </DialogDescription>
                        </DialogHeader>

                        <DialogFooter>
                            <DialogClose asChild>
                                <Button variant="outline">
                                    {t("drivers.confirm_delete.cancel")}
                                </Button>
                            </DialogClose>
                            <Button
                                variant="destructive"
                                onClick={deleteDriver}
                                disabled={isLoading}
                            >
                                {isLoading
                                    ? t("drivers.confirm_delete.deleting")
                                    : t("drivers.confirm_delete.delete")}
                            </Button>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    );
}
