import { openConfirmDeleteDriver<PERSON>tom } from "@/atoms/drivers/open-drivers-atoms";
import { selectedDriverAtom } from "@/atoms/drivers/selected-driver-atom";
import { driversAtom } from "@/atoms/drivers/drivers-atom";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function useConfirmDeleteDriverDialog() {
    const { t } = useTranslation();
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingFetchDriver, setIsLoadingFetchDriver] = useState(false);

    const { oneDriver } = driversAtom.useValue();
    const { slug } = selectedDriverAtom.useValue();
    const isOpened = openConfirmDeleteDriverAtom.useOpened();

    const closeDialog = () => {
        openConfirmDeleteDriverAtom.close();
        selectedDriverAtom.reset();
        driversAtom.change("oneDriver", null);
    };

    const deleteDriver = async () => {
        if (slug) {
            setIsLoading(true);
            await driversAtom.deleteDriver(slug, closeDialog);
            setIsLoading(false);
        }
    };

    useEffect(() => {
        async function fetchDriver() {
            if (slug) {
                setIsLoadingFetchDriver(true);
                await driversAtom.getOneDriver(slug);
                setIsLoadingFetchDriver(false);
            }
        }

        fetchDriver();
    }, [slug]);

    return {
        t,
        isLoading,
        isLoadingFetchDriver,
        oneDriver,
        isOpened,
        closeDialog,
        deleteDriver,
    };
}
