import {
  Dialog,
  <PERSON><PERSON>Close,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import useEditTrailerDialog from "@/hooks/trilers/use-edit-trailer-dialog";
import { Button } from "../ui/button";
import { Switch } from "../ui/switch";
import { Textarea } from "../ui/textarea";
import { OverlayLoader } from "../utils/overlay-loader";

export default function EditTrailerDialog() {
  const {
    closeDialog,
    form,
    isLoading,
    isLoadingFetchTrailer,
    isOpened,
    onSubmit,
  } = useEditTrailerDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Trailer</DialogTitle>
          <DialogDescription>
            Fill in the details to update the trailer.
          </DialogDescription>
        </DialogHeader>

        {isLoadingFetchTrailer && <OverlayLoader inCenter={false} />}

        {!isLoadingFetchTrailer && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter trailer name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter trailer description"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-2xl border p-3 shadow">
                    <div className="space-y-0.5">
                      <FormLabel>Is Active</FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline" type="button">
                    Cancel
                  </Button>
                </DialogClose>
                <Button variant="default" type="submit" disabled={isLoading}>
                  {isLoading ? "Updating..." : "Update"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
