export interface Driver {
  slug: string;
  name: string;
  driver_type: string;
  national_id: string;
  nationality: string;
  identity_number: string;
  address: string;
  is_active: boolean;
}

export interface GetAllDriversResponse {
  status_code: number;
  status: string;
  message: string | null;
  data: Driver[];
}

export interface GetOneDriverResponse {
  status_code: number;
  status: string;
  message: string;
  data: Driver;
}

export interface CreateDriverFormData {
  name: string;
  driver_type: string;
  national_id: string;
  nationality: string;
  identity_number: string;
  address: string;
  is_active: 0 | 1;
}

export interface CreateDriverResponse {
  status_code: number;
  status: string;
  message: string;
  data: Driver;
}

export interface UpdateDriverResponse {
  status_code: number;
  status: string;
  message: string;
  data: Driver;
}

export interface DeleteDriverResponse {
  status_code: number;
  status: string;
  message: string | null;
  data: string;
}
