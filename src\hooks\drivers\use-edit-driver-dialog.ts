import { openUpdateDriver<PERSON>tom } from "@/atoms/drivers/open-drivers-atoms";
import { selectedDriver<PERSON>tom } from "@/atoms/drivers/selected-driver-atom";
import { driversAtom } from "@/atoms/drivers/drivers-atom";
import type { CreateDriverFormData } from "@/types/drivers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const FormSchema = z.object({
    name: z.string().nonempty("Name is required"),
    driver_type: z.string().nonempty("Driver type is required"),
    national_id: z.string().nonempty("National ID is required"),
    nationality: z.string().nonempty("Nationality is required"),
    identity_number: z.string().nonempty("Identity number is required"),
    address: z.string().nonempty("Address is required"),
    is_active: z.boolean(),
}).required();

export default function useEditDriverDialog() {
    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zod<PERSON><PERSON>olver(FormSchema),
        defaultValues: {
            name: "",
            driver_type: "",
            national_id: "",
            nationality: "",
            identity_number: "",
            address: "",
            is_active: true,
        },
    });

    const { slug } = selectedDriverAtom.useValue();
    const { oneDriver } = driversAtom.useValue();

    const isOpened = openUpdateDriverAtom.useOpened();
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingFetchDriver, setIsLoadingFetchDriver] = useState(false);

    async function onSubmit(data: z.infer<typeof FormSchema>) {
        const formData: CreateDriverFormData = {
            name: data.name,
            driver_type: data.driver_type,
            national_id: data.national_id,
            nationality: data.nationality,
            identity_number: data.identity_number,
            address: data.address,
            is_active: data.is_active ? 1 : 0,
        };

        setIsLoading(true);

        if (slug) {
            await driversAtom.editDriver(slug, formData, () => {
                closeDialog();
            });
        }

        setIsLoading(false);
    }

    const closeDialog = () => {
        openUpdateDriverAtom.close();
        selectedDriverAtom.reset();
        driversAtom.change("oneDriver", null);
    };

    useEffect(() => {
        async function fetchDriver() {
            if (slug) {
                setIsLoadingFetchDriver(true);
                await driversAtom.getOneDriver(slug);
                setIsLoadingFetchDriver(false);
            }
        }

        fetchDriver();
    }, [slug]);

    useEffect(() => {
        if (oneDriver) {
            form.reset({
                name: oneDriver.name,
                driver_type: oneDriver.driver_type,
                national_id: oneDriver.national_id,
                nationality: oneDriver.nationality,
                identity_number: oneDriver.identity_number,
                address: oneDriver.address,
                is_active: oneDriver.is_active,
            });
        }
    }, [form, oneDriver]);

    return {
        form,
        onSubmit,
        closeDialog,
        isOpened,
        isLoading,
        isLoadingFetchDriver,
    };
}
