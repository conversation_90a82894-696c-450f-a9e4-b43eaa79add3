import {
    openConfirmDeleteDriver<PERSON>tom,
    openUpdateDriver<PERSON>tom,
} from "@/atoms/drivers/open-drivers-atoms";
import { selectedDriver<PERSON>tom } from "@/atoms/drivers/selected-driver-atom";
import { driversAtom } from "@/atoms/drivers/drivers-atom";
import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import ConfirmDeleteDriverDialog from "@/components/drivers/confirm-delete-driver-dialog";
import CreateDriverDialog from "@/components/drivers/create-driver-dialog";
import EditDriverDialog from "@/components/drivers/edit-driver-dialog";
import DriversPageHeader from "@/components/drivers/drivers-page-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
    ResizableHandle,
    ResizablePanel,
    ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
    Toolt<PERSON>,
    TooltipContent,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import { useIsMobile } from "@/hooks/use-mobile";
import useTableResize from "@/hooks/use-table-resize";
import type { Driver } from "@/types/drivers";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

const columns: ColumnDef<Driver>[] = [
    {
        id: "select",
        header: ({ table }) => (
            <Checkbox
                checked={
                    table.getIsAllPageRowsSelected() ||
                    (table.getIsSomePageRowsSelected() && "indeterminate")
                }
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
            />
        ),
        cell: ({ row }) => (
            <Checkbox
                checked={row.getIsSelected()}
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                aria-label="Select row"
            />
        ),
    },
    {
        accessorKey: "name",
        header: ({ column }) => {
            return <DataTableColumnHeader column={column} title="Name" />;
        },
        cell: ({ row }) => {
            const name = row.original.name;
            return <div>{name}</div>;
        },
    },
    {
        accessorKey: "is_active",
        header: ({ column }) => {
            return <DataTableColumnHeader column={column} title="Is Active" />;
        },
        cell: ({ row }) => {
            return <div>{row.original.is_active ? "True" : "False"}</div>;
        },
    },
    {
        id: "actions",
        header: () => <div className="text-start">Actions</div>,
        cell: ({ row }) => {
            return (
                <div className="flex gap-2">
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="secondary"
                                size="icon"
                                className="size-7"
                                onClick={() => {
                                    selectedDriverAtom.change("slug", row.original.slug);
                                    openUpdateDriverAtom.open();
                                }}
                            >
                                <Edit />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Edit</p>
                        </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="secondary"
                                size="icon"
                                className="size-7"
                                onClick={() => {
                                    selectedDriverAtom.change("slug", row.original.slug);
                                    openConfirmDeleteDriverAtom.open();
                                }}
                            >
                                <Trash2 color="red" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Delete</p>
                        </TooltipContent>
                    </Tooltip>
                </div>
            );
        },
    },
];

export default function DriversPage() {
    const isMobile = useIsMobile();
    const [isLoading, setIsLoading] = useState(false);

    const { handleResize, tableSize, minSize } = useTableResize();

    const { drivers } = driversAtom.useValue();

    useEffect(() => {
        async function fetchDrivers() {
            setIsLoading(true);
            await driversAtom.getDrivers();
            setIsLoading(false);
        }

        fetchDrivers();
    }, []);

    if (isLoading) {
        return <OverlayLoader />;
    }

    if (isMobile) {
        return (
            <div className="flex flex-col gap-3">
                <div className="flex flex-col gap-3 p-2">
                    <DriversPageHeader />
                    <DataTable columns={columns} data={drivers} />
                </div>
                <div className="h-[500px] w-full p-2">{/* <AreaMap /> */}</div>

                {/* <ConfirmDeleteAreaDialog /> */}

                <CreateDriverDialog />
                <EditDriverDialog />
                <ConfirmDeleteDriverDialog />
            </div>
        );
    }

    return (
        <ResizablePanelGroup direction="horizontal">
            <ResizablePanel
                className="flex flex-col gap-3 p-4"
                defaultSize={tableSize}
                onResize={handleResize}
                minSize={minSize}
            >
                <DriversPageHeader />
                <DataTable columns={columns} data={drivers} />
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel className="p-4">{/* <AreaMap /> */}</ResizablePanel>

            {/* <ConfirmDeleteAreaDialog /> */}

            <CreateDriverDialog />
            <EditDriverDialog />
            <ConfirmDeleteDriverDialog />
        </ResizablePanelGroup>
    );
}
