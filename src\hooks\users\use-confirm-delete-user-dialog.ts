import { openConfirmDeleteUser<PERSON>tom } from "@/atoms/users/open-users-atoms";
import { selectedUserAtom } from "@/atoms/users/selected-users-atom";
import { usersAtom } from "@/atoms/users/users-atom";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function useConfirmDeleteUserDialog() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchUser, setIsLoadingFetchUser] = useState(false);

  const { oneUser } = usersAtom.useValue();
  const { slug } = selectedUserAtom.useValue();
  const isOpened = openConfirmDeleteUserAtom.useOpened();

  const closeDialog = () => {
    openConfirmDeleteUserAtom.close();
    selectedUserAtom.reset();
    usersAtom.change("oneUser", null);
  };

  const deleteUser = async () => {
    if (slug) {
      setIsLoading(true);
      await usersAtom.deleteUser(slug, closeDialog);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    async function fetchUser() {
      if (slug) {
        setIsLoadingFetchUser(true);
        await usersAtom.getOneUser(slug);
        setIsLoadingFetchUser(false);
      }
    }

    fetchUser();
  }, [slug]);

  return {
    t,
    isLoading,
    isLoadingFetchUser,
    oneUser,
    isOpened,
    closeDialog,
    deleteUser,
  };
}
