import {
  openConfirmDeleteTrailer<PERSON>tom,
  openUpdateTrailerAtom,
} from "@/atoms/trailers/open-trailers-atoms";
import { selectedTrailerAtom } from "@/atoms/trailers/selected-trailer-atom";
import { trailresAtom } from "@/atoms/trailers/trailers-atom";
import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import ConfirmDeleteTrailerDialog from "@/components/trailers/confirm-delete-trailer-dialog";
import CreateTrailerDialog from "@/components/trailers/create-trailer-dialog";
import EditTrailerDialog from "@/components/trailers/edit-trailer-dialog";
import TrailersPageHeader from "@/components/trailers/trailers-page-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import { useIsMobile } from "@/hooks/use-mobile";
import useTableResize from "@/hooks/use-table-resize";
import type { Trailer } from "@/types/trailers";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

const columns: ColumnDef<Trailer>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Name" />;
    },
    cell: ({ row }) => {
      const name = row.original.name;
      return <div>{name}</div>;
    },
  },
  {
    accessorKey: "is_active",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Is Active" />;
    },
    cell: ({ row }) => {
      return <div>{row.original.is_active ? "True" : "False"}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-start">Actions</div>,
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="size-7"
                onClick={() => {
                  selectedTrailerAtom.change("slug", row.original.slug);
                  openUpdateTrailerAtom.open();
                }}
              >
                <Edit />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="size-7"
                onClick={() => {
                  selectedTrailerAtom.change("slug", row.original.slug);
                  openConfirmDeleteTrailerAtom.open();
                }}
              >
                <Trash2 color="red" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete</p>
            </TooltipContent>
          </Tooltip>
        </div>
      );
    },
  },
];

export default function TrailersPage() {
  const isMobile = useIsMobile();
  const [isLoading, setIsLoading] = useState(false);

  const { handleResize, tableSize, minSize } = useTableResize();

  const { trailers } = trailresAtom.useValue();

  useEffect(() => {
    async function fetchTrailers() {
      setIsLoading(true);
      await trailresAtom.getTrailers();
      setIsLoading(false);
    }

    fetchTrailers();
  }, []);

  if (isLoading) {
    return <OverlayLoader />;
  }

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3 p-2">
          <TrailersPageHeader />
          <DataTable columns={columns} data={trailers} />
        </div>
        <div className="h-[500px] w-full p-2">{/* <AreaMap /> */}</div>

        {/* <ConfirmDeleteAreaDialog /> */}

        <CreateTrailerDialog />
        <EditTrailerDialog />
        <ConfirmDeleteTrailerDialog />
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-3 p-4"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={minSize}
      >
        <TrailersPageHeader />
        <DataTable columns={columns} data={trailers} />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">{/* <AreaMap /> */}</ResizablePanel>

      {/* <ConfirmDeleteAreaDialog /> */}

      <CreateTrailerDialog />
      <EditTrailerDialog />
      <ConfirmDeleteTrailerDialog />
    </ResizablePanelGroup>
  );
}
