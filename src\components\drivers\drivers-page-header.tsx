import { But<PERSON> } from "@/components/ui/button";
import  useCreateDriverDialog from "@/hooks/drivers/use-create-driver-dialog";
import { Plus } from "lucide-react";

export default function DriversPageHeader() {
  const { openDialog } = useCreateDriverDialog();

  return (
    <div className="flex items-center justify-between">
      <h1 className="text-2xl font-bold tracking-tight">Drivers</h1>
      <Button onClick={openDialog}>
        <Plus className="mr-2 h-4 w-4" />
        Add Driver
      </Button>
    </div>
  );
}
