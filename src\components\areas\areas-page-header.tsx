import { URLS } from "@/utils/urls";
import { Plus } from "lucide-react";
import { <PERSON> } from "react-router";
import { Button } from "../ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

export default function AreasPageHeader() {
  return (
    <div className="flex items-center justify-between">
      <h1 className="text-primary text-2xl font-bold">Areas</h1>
      <div className="flex gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Link to={URLS.addArea}>
              <Button variant="secondary" size="icon" className="size-7">
                <Plus />
              </Button>
            </Link>
          </TooltipTrigger>
          <TooltipContent>
            <p>Add Area</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}
