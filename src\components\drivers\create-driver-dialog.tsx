import {
    Dialog,
    Di<PERSON>Close,
    DialogContent,
    <PERSON>alogDescription,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import useCreateDriverDialog from "@/hooks/drivers/use-create-driver-dialog";
import { Button } from "../ui/button";
import { Switch } from "../ui/switch";
import { Textarea } from "../ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

export default function CreateDriverDialog() {
  const { form, isLoading, isOpened, closeDialog, onSubmit } = useCreateDriverDialog();

  return (
      <Dialog open={isOpened} onOpenChange={closeDialog}>
          <DialogContent>
              <DialogHeader>
                  <DialogTitle>Create Driver</DialogTitle>
                  <DialogDescription>
                      Fill in the details to create a new driver.
                  </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                              control={form.control}
                              name="name"
                              render={({ field }) => (
                                  <FormItem>
                                      <FormLabel>Full Name</FormLabel>
                                      <FormControl>
                                          <Input placeholder="Enter driver's full name" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                  </FormItem>
                              )}
                          />
                          <FormField
                              control={form.control}
                              name="driver_type"
                              render={({ field }) => (
                                  <FormItem>
                                      <FormLabel>Driver Type</FormLabel>
                                      <Select onValueChange={field.onChange} value={field.value}>
                                          <FormControl>
                                              <SelectTrigger>
                                                  <SelectValue placeholder="Select driver type" />
                                              </SelectTrigger>
                                          </FormControl>
                                          <SelectContent>
                                              <SelectItem value="1">Employee</SelectItem>
                                              <SelectItem value="2">Subcontractor</SelectItem>
                                          </SelectContent>
                                      </Select>
                                      <FormMessage />
                                  </FormItem>
                              )}
                          />
                          <FormField
                              control={form.control}
                              name="national_id"
                              render={({ field }) => (
                                  <FormItem>
                                      <FormLabel>National ID</FormLabel>
                                      <FormControl>
                                          <Input placeholder="Enter national ID" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                  </FormItem>
                              )}
                          />
                          <FormField
                              control={form.control}
                              name="nationality"
                              render={({ field }) => (
                                  <FormItem>
                                      <FormLabel>Nationality</FormLabel>
                                      <FormControl>
                                          <Input placeholder="Enter nationality" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                  </FormItem>
                              )}
                          />
                          <FormField
                              control={form.control}
                              name="identity_number"
                              render={({ field }) => (
                                  <FormItem>
                                      <FormLabel>Identity Number</FormLabel>
                                      <FormControl>
                                          <Input placeholder="Enter identity number" {...field} />
                                      </FormControl>
                                      <FormMessage />
                                  </FormItem>
                              )}
                          />
                          <FormField
                              control={form.control}
                              name="address"
                              render={({ field }) => (
                                  <FormItem className="md:col-span-2">
                                      <FormLabel>Address</FormLabel>
                                      <FormControl>
                                          <Textarea
                                              placeholder="Enter full address"
                                              {...field}
                                          />
                                      </FormControl>
                                      <FormMessage />
                                  </FormItem>
                              )}
                          />
                      </div>
                      <FormField
                          control={form.control}
                          name="is_active"
                          render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-2xl border p-3 shadow">
                                  <div className="space-y-0.5">
                                      <FormLabel>Is Active</FormLabel>
                                  </div>
                                  <FormControl>
                                      <Switch
                                          checked={field.value}
                                          onCheckedChange={field.onChange}
                                      />
                                  </FormControl>
                              </FormItem>
                          )}
                      />
                      <DialogFooter>
                          <DialogClose asChild>
                              <Button variant="outline" type="button">
                                  Cancel
                              </Button>
                          </DialogClose>
                          <Button variant="default" type="submit" disabled={isLoading}>
                              {isLoading ? "Creating..." : "Create"}
                          </Button>
                      </DialogFooter>
                  </form>
              </Form>
          </DialogContent>
      </Dialog>
  );
}
