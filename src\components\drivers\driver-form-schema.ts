import { z } from "zod";

export const driverFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  driver_type: z.string().min(1, "Driver type is required"),
  national_id: z.string().min(1, "National ID is required"),
  nationality: z.string().min(1, "Nationality is required"),
  identity_number: z.string().min(1, "Identity number is required"),
  address: z.string().min(1, "Address is required"),
  is_active: z.boolean().default(true),
});

export type DriverFormValues = z.infer<typeof driverFormSchema>;
