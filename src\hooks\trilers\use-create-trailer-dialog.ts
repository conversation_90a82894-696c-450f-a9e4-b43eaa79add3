import { openCreateTrailer<PERSON>tom } from "@/atoms/trailers/open-trailers-atoms";
import { trailresAtom } from "@/atoms/trailers/trailers-atom";
import type { CreateTrailerFormData } from "@/types/trailers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const FormSchema = z.object({
  name: z.string().nonempty("Name is required"),
  description: z.string().nonempty("Description is required"),
  is_active: z.boolean(),
});

export default function useCreateTrailerDialog() {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: false,
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const isOpened = openCreateTrailerAtom.useOpened();

  const closeDialog = () => {
    openCreateTrailerAtom.close();
    form.reset();
  };

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: CreateTrailerFormData = {
      name: data.name,
      description: data.description,
      is_active: data.is_active ? 1 : 0,
    };

    setIsLoading(true);

    await trailresAtom.createTrailer(formData, () => {
      closeDialog();
    });

    setIsLoading(false);
  }

  return {
    form,
    isLoading,
    isOpened,
    closeDialog,
    onSubmit,
  };
}
