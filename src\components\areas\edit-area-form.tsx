import { selectedDrawer<PERSON><PERSON><PERSON><PERSON> } from "@/atoms/areas/selected-drawer-type-atom";
import { Button } from "@/components/ui/button";
import { ColorInput } from "@/components/ui/color-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import useEditAreaForm from "@/hooks/areas/use-edit-area-form";
import { URLS } from "@/utils/urls";
import { Link } from "react-router";
import { OverlayLoader } from "../utils/overlay-loader";

export default function EditAreaForm() {
  const { areaGroups, form, isLoading, isLoadingFetchArea, onSubmit } =
    useEditAreaForm();

  if (isLoadingFetchArea) {
    return (
      <div className="relative flex h-full items-center justify-center">
        <OverlayLoader />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter area name" {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl>
                  <Input placeholder="Enter area code" {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(Number(value));
                    selectedDrawerTypeAtom.change(
                      "selectedType",
                      Number(value),
                    );
                  }}
                  value={field.value > 0 ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="1">circle</SelectItem>
                    <SelectItem value="2">polygon</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="area_group_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Area Group</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(Number(value))}
                  value={field.value > 0 ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Area Group" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {areaGroups?.map((group) => (
                      <SelectItem key={group.id} value={String(group.id)}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Color</FormLabel>
                <FormControl>
                  <ColorInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select color"
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="is_personal"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-2xl border p-3 shadow">
              <div className="space-y-0.5">
                <FormLabel>Is Personal</FormLabel>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading} variant={"outline"}>
            {isLoading ? "Editing..." : "Edit"}
          </Button>
          <Link to={URLS.areas}>
            <Button type="button" variant={"outline"}>
              Back
            </Button>
          </Link>
        </div>
      </form>
    </Form>
  );
}
