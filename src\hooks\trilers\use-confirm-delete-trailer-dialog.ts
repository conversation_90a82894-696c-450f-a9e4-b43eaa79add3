import { openConfirmDeleteTrailerAtom } from "@/atoms/trailers/open-trailers-atoms";
import { selectedTrailerAtom } from "@/atoms/trailers/selected-trailer-atom";
import { trailresAtom } from "@/atoms/trailers/trailers-atom";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function useConfirmDeleteTrailerDialog() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchTrailer, setIsLoadingFetchTrailer] = useState(false);

  const { oneTrailer } = trailresAtom.useValue();
  const { slug } = selectedTrailerAtom.useValue();
  const isOpened = openConfirmDeleteTrailerAtom.useOpened();

  const closeDialog = () => {
    openConfirmDeleteTrailerAtom.close();
    selectedTrailerAtom.reset();
    trailresAtom.change("oneTrailer", null);
  };

  const deleteTrailer = async () => {
    if (slug) {
      setIsLoading(true);
      await trailresAtom.deleteTrailer(slug, closeDialog);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    async function fetchTrailer() {
      if (slug) {
        setIsLoadingFetchTrailer(true);
        await trailresAtom.getOneTrailer(slug);
        setIsLoadingFetchTrailer(false);
      }
    }

    fetchTrailer();
  }, [slug]);

  return {
    t,
    isLoading,
    isLoadingFetchTrailer,
    oneTrailer,
    isOpened,
    closeDialog,
    deleteTrailer,
  };
}
