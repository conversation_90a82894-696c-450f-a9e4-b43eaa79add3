import { openCreate<PERSON><PERSON><PERSON>tom } from "@/atoms/drivers/open-drivers-atoms";
import { drivers<PERSON>tom } from "@/atoms/drivers/drivers-atom";
import type { CreateDriverFormData } from "@/types/drivers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const FormSchema = z.object({
    name: z.string().nonempty("Name is required"),
    driver_type: z.string().nonempty("Driver type is required"),
    national_id: z.string().nonempty("National ID is required"),
    nationality: z.string().nonempty("Nationality is required"),
    identity_number: z.string().nonempty("Identity number is required"),
    address: z.string().nonempty("Address is required"),
    is_active: z.boolean(),
});

export default function useCreateDriverDialog() {
    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: {
            name: "",
            driver_type: "",
            national_id: "",
            nationality: "",
            identity_number: "",
            address: "",
            is_active: false,
        },
    });

    const [isLoading, setIsLoading] = useState(false);
    const isOpened = openCreateDriverAtom.useOpened();

    const closeDialog = () => {
        openCreateDriverAtom.close();
        form.reset();
    };

    async function onSubmit(data: z.infer<typeof FormSchema>) {
        const formData: CreateDriverFormData = {
            name: data.name,
            driver_type: data.driver_type,
            national_id: data.national_id,
            nationality: data.nationality,
            identity_number: data.identity_number,
            address: data.address,
            is_active: data.is_active ? 1 : 0,
        };

        setIsLoading(true);

        await driversAtom.createDriver(formData, () => {
            closeDialog();
        });

        setIsLoading(false);
    }

    const openDialog = () => {
        openCreateDriverAtom.open();
    };

    return {
        form,
        isLoading,
        isOpened,
        openDialog,
        closeDialog,
        onSubmit,
    };
}
