import type {
  CreateDriverFormData,
  CreateDriverResponse,
  DeleteDriverResponse,
  Driver,
  GetAllDriversResponse,
  GetOneDriverResponse,
  UpdateDriverResponse,
} from "@/types/drivers";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface DriversAtom {
  drivers: Driver[];
  oneDriver: Driver | null;
}

type DriversAtomAction = {
  getDrivers: () => Promise<void>;
  getOneDriver: (slug: string) => Promise<void>;
  createDriver: (formData: CreateDriverFormData, onSuccess?: () => void) => Promise<void>;
  editDriver: (slug: string, formData: CreateDriverFormData, onSuccess?: () => void) => Promise<void>;
  deleteDriver: (slug: string, onSuccess?: () => void) => Promise<void>;
};

export const driversAtom = atom<DriversAtom, DriversAtomAction>({
  key: "drivers-atom",
  default: {
    drivers: [],
    oneDriver: null,
  },

  actions: {
    async getDrivers() {
      try {
        const { data } = await endpoint.get<GetAllDriversResponse>("drivers");
        driversAtom.change("drivers", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async getOneDriver(slug: string) {
      try {
        const { data } = await endpoint.get<GetOneDriverResponse>(`drivers/${slug}`);
        driversAtom.change("oneDriver", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async createDriver(formData: CreateDriverFormData, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.post<CreateDriverResponse>("drivers", formData);
        toast.success(data.message);
        onSuccess?.();
        driversAtom.getDrivers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async editDriver(slug: string, formData: CreateDriverFormData, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.put<UpdateDriverResponse>(`drivers/${slug}`, formData);
        toast.success(data.message);
        onSuccess?.();
        driversAtom.getDrivers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async deleteDriver(slug: string, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.delete<DeleteDriverResponse>(`drivers/${slug}`);
        toast.success(data.data);
        onSuccess?.();
        driversAtom.getDrivers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
